{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 451249050788002760, "deps": [[654232091421095663, "tauri_utils", false, 15308955495522971671], [3150220818285335163, "url", false, 12663329439391652794], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [7606335748176206944, "dpi", false, 12077686462733247621], [8569119365930580996, "serde_json", false, 13571012663895458450], [9010263965687315507, "http", false, 1862789691165185595], [9689903380558560274, "serde", false, 9841288889961963921], [10806645703491011684, "thiserror", false, 13847597657789702417], [12943761728066819757, "build_script_build", false, 1932772659449095175], [14585479307175734061, "windows", false, 3114519431610511065], [16727543399706004146, "cookie", false, 13861560567671079529]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-91748cec27440a0a\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}