{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 1660944289106784126, "deps": [[3150220818285335163, "url", false, 8742444725310900714], [6913375703034175521, "build_script_build", false, 4104668329138326016], [8319709847752024821, "uuid1", false, 1753730045019586743], [8569119365930580996, "serde_json", false, 3825096068333769646], [9122563107207267705, "dyn_clone", false, 9387001955209314550], [9689903380558560274, "serde", false, 14955956804811053645], [14923790796823607459, "indexmap", false, 16904037970292752923], [16071897500792579091, "schemars_derive", false, 11658860870869054554]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-c3df933e174c339b\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}