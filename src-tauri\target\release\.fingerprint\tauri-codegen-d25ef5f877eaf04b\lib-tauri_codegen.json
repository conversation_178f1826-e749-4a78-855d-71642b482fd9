{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 5726785450906783363, "deps": [[654232091421095663, "tauri_utils", false, 5433344512091385606], [3060637413840920116, "proc_macro2", false, 5096312196297537516], [3150220818285335163, "url", false, 14894005264751682384], [4899080583175475170, "semver", false, 10438149423428263558], [4974441333307933176, "syn", false, 946335139158440780], [7170110829644101142, "json_patch", false, 6043411410925377948], [7392050791754369441, "ico", false, 3682478314545997282], [8319709847752024821, "uuid", false, 15291427409137580751], [8569119365930580996, "serde_json", false, 15898548419435081726], [9556762810601084293, "brotli", false, 14777250860789216788], [9689903380558560274, "serde", false, 101596170299597295], [9857275760291862238, "sha2", false, 17140178370350060100], [10806645703491011684, "thiserror", false, 7518697846591293610], [12687914511023397207, "png", false, 12951014472576175494], [13077212702700853852, "base64", false, 18162460141959120734], [15622660310229662834, "walkdir", false, 4267858985654455119], [17990358020177143287, "quote", false, 9304775612473768952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-d25ef5f877eaf04b\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}