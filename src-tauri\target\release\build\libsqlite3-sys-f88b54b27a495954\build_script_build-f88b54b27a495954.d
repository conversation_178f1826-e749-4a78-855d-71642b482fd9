D:\Users\Administrator\Desktop\MoReader\src-tauri\target\release\build\libsqlite3-sys-f88b54b27a495954\build_script_build-f88b54b27a495954.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.28.0\build.rs

D:\Users\Administrator\Desktop\MoReader\src-tauri\target\release\build\libsqlite3-sys-f88b54b27a495954\build_script_build-f88b54b27a495954.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.28.0\build.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.28.0\build.rs:

# env-dep:CARGO_MANIFEST_DIR=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libsqlite3-sys-0.28.0
