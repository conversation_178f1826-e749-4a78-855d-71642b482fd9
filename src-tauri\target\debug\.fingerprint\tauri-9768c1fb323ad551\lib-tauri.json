{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 17160455492222233904, "deps": [[40386456601120721, "percent_encoding", false, 15092931327459917350], [654232091421095663, "tauri_utils", false, 11379566753883280293], [1200537532907108615, "url<PERSON><PERSON>n", false, 15381619336220301248], [2013030631243296465, "webview2_com", false, 3252816996939302882], [3150220818285335163, "url", false, 16277201914255492281], [3331586631144870129, "getrandom", false, 856317537623502267], [4143744114649553716, "raw_window_handle", false, 3687742720338333907], [4494683389616423722, "muda", false, 3338705099332980988], [4919829919303820331, "serialize_to_javascript", false, 8479418753913360912], [5986029879202738730, "log", false, 13499195873745698737], [8569119365930580996, "serde_json", false, 11475744921138336352], [9010263965687315507, "http", false, 4349961928876268954], [9689903380558560274, "serde", false, 6764526651753107293], [10229185211513642314, "mime", false, 2311114312296159829], [10806645703491011684, "thiserror", false, 2658131691907457673], [11989259058781683633, "dunce", false, 11458171079510533241], [12092653563678505622, "build_script_build", false, 4498366672870285246], [12304025191202589669, "tauri_runtime_wry", false, 7898855030140676467], [12393800526703971956, "tokio", false, 3080444982321408450], [12565293087094287914, "window_vibrancy", false, 14953500343473293690], [12943761728066819757, "tauri_runtime", false, 10023275760444581229], [12986574360607194341, "serde_repr", false, 5766458852573111782], [13077543566650298139, "heck", false, 7013131016141125845], [13405681745520956630, "tauri_macros", false, 8634633619867886938], [13625485746686963219, "anyhow", false, 4098271231730516493], [14585479307175734061, "windows", false, 8237828940172028574], [16928111194414003569, "dirs", false, 15323113001723499904], [17155886227862585100, "glob", false, 4517372238046053402]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-9768c1fb323ad551\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}