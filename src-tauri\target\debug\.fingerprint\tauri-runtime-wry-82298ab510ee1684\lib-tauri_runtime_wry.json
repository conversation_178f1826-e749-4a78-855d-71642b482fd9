{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 8261735638378605462, "deps": [[376837177317575824, "softbuffer", false, 16299997524267654735], [654232091421095663, "tauri_utils", false, 11379566753883280293], [2013030631243296465, "webview2_com", false, 3252816996939302882], [3150220818285335163, "url", false, 16277201914255492281], [3722963349756955755, "once_cell", false, 13244492203321078007], [4143744114649553716, "raw_window_handle", false, 3687742720338333907], [5986029879202738730, "log", false, 13499195873745698737], [8826339825490770380, "tao", false, 8208698662963689078], [9010263965687315507, "http", false, 4349961928876268954], [9141053277961803901, "wry", false, 11776838090991610289], [12304025191202589669, "build_script_build", false, 14405279426092763355], [12943761728066819757, "tauri_runtime", false, 10023275760444581229], [14585479307175734061, "windows", false, 8237828940172028574]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-82298ab510ee1684\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}