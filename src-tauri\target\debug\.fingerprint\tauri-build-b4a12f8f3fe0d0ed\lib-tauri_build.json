{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 15561909567757332897, "deps": [[654232091421095663, "tauri_utils", false, 15481792539825832463], [4899080583175475170, "semver", false, 16636169827845809847], [6913375703034175521, "schemars", false, 8067873719202499382], [7170110829644101142, "json_patch", false, 10653267478274599111], [8569119365930580996, "serde_json", false, 3825096068333769646], [9689903380558560274, "serde", false, 14955956804811053645], [12714016054753183456, "tauri_winres", false, 1459555481190829724], [13077543566650298139, "heck", false, 8749295945489699306], [13475171727366188400, "cargo_toml", false, 16401026469176871385], [13625485746686963219, "anyhow", false, 11841672266104157151], [15609422047640926750, "toml", false, 5838905183159045830], [15622660310229662834, "walkdir", false, 18042159149950293803], [16928111194414003569, "dirs", false, 2510716140661615147], [17155886227862585100, "glob", false, 5597488296261464133]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-b4a12f8f3fe0d0ed\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}