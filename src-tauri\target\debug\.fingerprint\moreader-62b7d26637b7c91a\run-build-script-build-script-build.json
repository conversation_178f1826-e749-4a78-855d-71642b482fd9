{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9499578842231298228, "build_script_build", false, 11326348394311744004], [12092653563678505622, "build_script_build", false, 4498366672870285246], [8324462083842905811, "build_script_build", false, 6117640616656310289]], "local": [{"RerunIfChanged": {"output": "debug\\build\\moreader-62b7d26637b7c91a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}