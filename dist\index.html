<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>墨读 - MoReader</title>
    <link rel="stylesheet" href="src/styles/main.css">
</head>
<body>
    <div id="app">
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">墨读</h1>
                <div class="header-actions">
                    <button id="import-btn" class="btn btn-primary">导入书籍</button>
                    <button id="settings-btn" class="btn btn-secondary">设置</button>
                </div>
            </div>
        </header>

        <main class="app-main">
            <aside class="sidebar">
                <nav class="nav-menu">
                    <ul>
                        <li><a href="#library" class="nav-link active">书架</a></li>
                        <li><a href="#recent" class="nav-link">最近阅读</a></li>
                        <li><a href="#favorites" class="nav-link">收藏</a></li>
                        <li><a href="#notes" class="nav-link">笔记</a></li>
                    </ul>
                </nav>
            </aside>

            <section class="content-area">
                <div id="library-view" class="view active">
                    <div class="view-header">
                        <h2>我的书架</h2>
                        <div class="view-controls">
                            <select id="sort-select">
                                <option value="recent">最近添加</option>
                                <option value="name">书名</option>
                                <option value="author">作者</option>
                            </select>
                            <div class="view-mode-toggle">
                                <button id="grid-view" class="view-btn active">网格</button>
                                <button id="list-view" class="view-btn">列表</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="books-container" id="books-container">
                        <div class="empty-state">
                            <div class="empty-icon">📚</div>
                            <h3>书架空空如也</h3>
                            <p>点击"导入书籍"开始您的阅读之旅</p>
                            <button class="btn btn-primary">导入第一本书</button>
                        </div>
                    </div>
                </div>

                <div id="reader-view" class="view">
                    <div class="reader-container">
                        <div class="reader-toolbar">
                            <button id="back-to-library">← 返回书架</button>
                            <div class="reader-progress">
                                <span id="current-page">1</span> / <span id="total-pages">100</span>
                            </div>
                            <div class="reader-controls">
                                <button id="bookmark-btn">🔖</button>
                                <button id="note-btn">📝</button>
                                <button id="tts-btn">🔊</button>
                            </div>
                        </div>
                        
                        <div class="reader-content" id="reader-content">
                            <!-- 阅读内容将在这里动态加载 -->
                        </div>
                        
                        <div class="reader-navigation">
                            <button id="prev-page" class="nav-btn">上一页</button>
                            <button id="next-page" class="nav-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="src/main.js"></script>
    <script>
        // 确保在Tauri环境中正确初始化
        if (window.__TAURI__) {
            console.log('在Tauri环境中运行')
        } else {
            console.log('在浏览器环境中运行（开发模式）')
        }

        // 添加全局错误处理
        window.addEventListener('error', (e) => {
            console.error('全局错误:', e.error)
        })

        // 添加点击调试
        document.addEventListener('click', (e) => {
            console.log('点击事件:', e.target.tagName, e.target.className, e.target.id)
        })
    </script>
</body>
</html>
