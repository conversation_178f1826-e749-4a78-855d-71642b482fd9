{"$message_type":"diagnostic","message":"unused import: `parser::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\mod.rs","byte_start":97,"byte_end":106,"line_start":7,"line_end":7,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use parser::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\core\\mod.rs","byte_start":89,"byte_end":108,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use parser::*;","highlight_start":1,"highlight_end":19},{"text":"pub use storage::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `parser::*`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\mod.rs:7:9\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use parser::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":5265,"byte_end":5266,"line_start":154,"line_end":154,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":5265,"byte_end":5266,"line_start":154,"line_end":154,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:154:103\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m:<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Tex\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":5495,"byte_end":5496,"line_start":155,"line_end":155,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":5495,"byte_end":5496,"line_start":155,"line_end":155,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:155:103\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m:<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Tex\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":6982,"byte_end":6983,"line_start":189,"line_end":189,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":6982,"byte_end":6983,"line_start":189,"line_end":189,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:189:103\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m:<_, String>(14)?).map_err(|e| rusqlite::Error::InvalidColumnType(14, \"created_at\".to_string(), rusqlite::types::Type::Tex\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":7212,"byte_end":7213,"line_start":190,"line_end":190,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":7212,"byte_end":7213,"line_start":190,"line_end":190,"column_start":103,"column_end":104,"is_primary":true,"text":[{"text":"                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":103,"highlight_end":104}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:190:103\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m:<_, String>(15)?).map_err(|e| rusqlite::Error::InvalidColumnType(15, \"updated_at\".to_string(), rusqlite::types::Type::Tex\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":9326,"byte_end":9327,"line_start":240,"line_end":240,"column_start":104,"column_end":105,"is_primary":true,"text":[{"text":"                last_read_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"last_read_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":104,"highlight_end":105}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":9326,"byte_end":9327,"line_start":240,"line_end":240,"column_start":104,"column_end":105,"is_primary":true,"text":[{"text":"                last_read_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"last_read_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":104,"highlight_end":105}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:240:104\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"last_read_at\".to_string(), rusqlite::types::Type::Te\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\storage.rs","byte_start":10849,"byte_end":10850,"line_start":282,"line_end":282,"column_start":102,"column_end":103,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":102,"highlight_end":103}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\storage.rs","byte_start":10849,"byte_end":10850,"line_start":282,"line_end":282,"column_start":102,"column_end":103,"is_primary":true,"text":[{"text":"                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"created_at\".to_string(), rusqlite::types::Type::Text))?.with_timezone(&chrono::Utc),","highlight_start":102,"highlight_end":103}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\storage.rs:282:102\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m::<_, String>(5)?).map_err(|e| rusqlite::Error::InvalidColumnType(5, \"created_at\".to_string(), rusqlite::types::Type::Text\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\n"}
