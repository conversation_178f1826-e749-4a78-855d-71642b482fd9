{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 8261735638378605462, "deps": [[376837177317575824, "softbuffer", false, 11067749456232259263], [654232091421095663, "tauri_utils", false, 15308955495522971671], [2013030631243296465, "webview2_com", false, 8039702730747510435], [3150220818285335163, "url", false, 12663329439391652794], [3722963349756955755, "once_cell", false, 10802249507119394238], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [5986029879202738730, "log", false, 15905267756897252431], [8826339825490770380, "tao", false, 16201509635147266337], [9010263965687315507, "http", false, 1862789691165185595], [9141053277961803901, "wry", false, 5833422956787195621], [12304025191202589669, "build_script_build", false, 17205353052212004681], [12943761728066819757, "tauri_runtime", false, 643590536472946368], [14585479307175734061, "windows", false, 3114519431610511065]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-b76fcea6ccbbeb65\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}