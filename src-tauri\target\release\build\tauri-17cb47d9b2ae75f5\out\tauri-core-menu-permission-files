["\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\menu\\autogenerated\\default.toml"]