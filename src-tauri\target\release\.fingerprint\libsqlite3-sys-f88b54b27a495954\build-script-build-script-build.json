{"rustc": 1842507548689473721, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 974437933323113654, "deps": [[3214373357989284387, "pkg_config", false, 10782605816820488365], [12933202132622624734, "vcpkg", false, 3680487747539977777], [15056754423999335055, "cc", false, 17765546333719546954]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\libsqlite3-sys-f88b54b27a495954\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}