{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 14215436630019529674, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15381619336220301248], [3150220818285335163, "url", false, 16277201914255492281], [3191507132440681679, "serde_untagged", false, 15284311287432959195], [4071963112282141418, "serde_with", false, 17448937688548032619], [4899080583175475170, "semver", false, 3666962832039582367], [5986029879202738730, "log", false, 13499195873745698737], [6606131838865521726, "ctor", false, 8001462743388477718], [7170110829644101142, "json_patch", false, 6414344316347076467], [8319709847752024821, "uuid", false, 14569418664286130381], [8569119365930580996, "serde_json", false, 11475744921138336352], [9010263965687315507, "http", false, 4349961928876268954], [9451456094439810778, "regex", false, 2728588077459236407], [9556762810601084293, "brotli", false, 16837571029197592200], [9689903380558560274, "serde", false, 6764526651753107293], [10806645703491011684, "thiserror", false, 2658131691907457673], [11989259058781683633, "dunce", false, 11458171079510533241], [13625485746686963219, "anyhow", false, 4098271231730516493], [15609422047640926750, "toml", false, 17413456271808637879], [15622660310229662834, "walkdir", false, 11143037802736821657], [15932120279885307830, "memchr", false, 3753662393507526803], [17146114186171651583, "infer", false, 4221753414867047915], [17155886227862585100, "glob", false, 4517372238046053402], [17186037756130803222, "phf", false, 2044827827291456774]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-d2d26deb20c039ea\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}