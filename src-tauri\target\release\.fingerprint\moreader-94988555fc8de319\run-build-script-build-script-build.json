{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9499578842231298228, "build_script_build", false, 10839942515901335227], [12092653563678505622, "build_script_build", false, 5543743481887360298], [9611038886379759122, "build_script_build", false, 10370697493822592633], [12504415026414629397, "build_script_build", false, 9926639035034632438], [8324462083842905811, "build_script_build", false, 13158886572439682433]], "local": [{"RerunIfChanged": {"output": "release\\build\\moreader-94988555fc8de319\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}