["\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\Users\\Administrator\\Desktop\\MoReader\\src-tauri\\target\\release\\build\\tauri-17cb47d9b2ae75f5\\out\\permissions\\path\\autogenerated\\default.toml"]