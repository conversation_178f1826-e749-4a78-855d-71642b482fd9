use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 书籍元数据
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BookMetadata {
    pub id: String,
    pub title: String,
    pub author: String,
    pub description: Option<String>,
    pub language: Option<String>,
    pub publisher: Option<String>,
    pub published_date: Option<String>,
    pub isbn: Option<String>,
    pub cover_path: Option<String>,
    pub file_path: String,
    pub file_format: FileFormat,
    pub file_size: u64,
    pub total_pages: Option<u32>,
    pub total_chapters: Option<u32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 支持的文件格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileFormat {
    Epub,
    Pdf,
    Txt,
    Mobi,
    Azw3,
    Cbr,
    Cbz,
    Docx,
    Html,
}

impl FileFormat {
    pub fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "epub" => Some(Self::Epub),
            "pdf" => Some(Self::Pdf),
            "txt" => Some(Self::Txt),
            "mobi" => Some(Self::Mobi),
            "azw3" => Some(Self::Azw3),
            "cbr" => Some(Self::Cbr),
            "cbz" => Some(Self::Cbz),
            "docx" => Some(Self::Docx),
            "html" | "htm" => Some(Self::Html),
            _ => None,
        }
    }
}

/// 阅读进度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReadingProgress {
    pub book_id: String,
    pub current_page: u32,
    pub current_chapter: Option<u32>,
    pub progress_percentage: f32,
    pub reading_time_minutes: u32,
    pub last_read_at: DateTime<Utc>,
}

/// 书签
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bookmark {
    pub id: String,
    pub book_id: String,
    pub page_number: u32,
    pub chapter_title: Option<String>,
    pub note: Option<String>,
    pub created_at: DateTime<Utc>,
}

/// 笔记/标注
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Annotation {
    pub id: String,
    pub book_id: String,
    pub page_number: u32,
    pub start_offset: u32,
    pub end_offset: u32,
    pub selected_text: String,
    pub note: Option<String>,
    pub highlight_color: Option<String>,
    pub annotation_type: AnnotationType,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnnotationType {
    Highlight,
    Note,
    Underline,
    Strikethrough,
}

/// 阅读统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReadingStats {
    pub total_books: u32,
    pub total_reading_time_minutes: u32,
    pub books_completed: u32,
    pub average_reading_speed_wpm: f32,
    pub favorite_genres: Vec<String>,
    pub reading_streak_days: u32,
    pub last_updated: DateTime<Utc>,
}

/// 文档内容元素
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContentElement {
    Text {
        content: String,
        style: Option<TextStyle>,
    },
    Image {
        src: String,
        alt: Option<String>,
        width: Option<u32>,
        height: Option<u32>,
    },
    Heading {
        level: u8,
        content: String,
    },
    Paragraph {
        content: String,
        style: Option<TextStyle>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextStyle {
    pub font_family: Option<String>,
    pub font_size: Option<f32>,
    pub font_weight: Option<String>,
    pub color: Option<String>,
    pub background_color: Option<String>,
    pub italic: bool,
    pub bold: bool,
    pub underline: bool,
}

/// 页面内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageContent {
    pub page_number: u32,
    pub elements: Vec<ContentElement>,
    pub chapter_title: Option<String>,
}

/// 应用设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    pub theme: String,
    pub font_family: String,
    pub font_size: f32,
    pub line_height: f32,
    pub page_margins: PageMargins,
    pub reading_mode: ReadingMode,
    pub auto_save_progress: bool,
    pub tts_enabled: bool,
    pub tts_voice: Option<String>,
    pub tts_speed: f32,
    pub language: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageMargins {
    pub top: f32,
    pub bottom: f32,
    pub left: f32,
    pub right: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReadingMode {
    SinglePage,
    DoublePage,
    Scroll,
    Continuous,
}
