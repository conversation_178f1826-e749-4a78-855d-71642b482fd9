{"rustc": 1842507548689473721, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2040997289075261528, "path": 13487783198191154298, "deps": [[3056352129074654578, "hashlink", false, 18374605998675814229], [3666196340704888985, "smallvec", false, 17567809383700836649], [5510864063823219921, "fallible_streaming_iterator", false, 354694965122583712], [7896293946984509699, "bitflags", false, 4405188476997761181], [9986166984836792091, "libsqlite3_sys", false, 8792095073928966301], [12860549049674006569, "fallible_iterator", false, 2956096768141414857]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rusqlite-4d299206f7c687e3\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}