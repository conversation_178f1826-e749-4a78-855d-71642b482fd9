{"rustc": 1842507548689473721, "features": "[\"ahash\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2040997289075261528, "path": 2487185053407325305, "deps": [[966925859616469517, "ahash", false, 8394703455020444003]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-9c1bf564b86142ba\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}