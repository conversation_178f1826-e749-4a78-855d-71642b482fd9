<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>墨读 - 测试页面</title>
    <link rel="stylesheet" href="src/styles/main.css">
</head>
<body>
    <div id="app">
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">墨读 (测试模式)</h1>
                <div class="header-actions">
                    <button id="import-btn" class="btn btn-primary">导入书籍</button>
                    <button id="settings-btn" class="btn btn-secondary">设置</button>
                </div>
            </div>
        </header>

        <main class="app-main">
            <aside class="sidebar">
                <nav class="nav-menu">
                    <ul>
                        <li><a href="#library" class="nav-link active">书架</a></li>
                        <li><a href="#recent" class="nav-link">最近阅读</a></li>
                        <li><a href="#favorites" class="nav-link">收藏</a></li>
                        <li><a href="#notes" class="nav-link">笔记</a></li>
                    </ul>
                </nav>
                
                <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 8px; font-size: 12px;">
                    <strong>测试说明：</strong><br>
                    • 点击导航链接测试视图切换<br>
                    • 点击"导入书籍"测试文件选择<br>
                    • 点击视图切换按钮测试布局<br>
                    • 查看控制台日志了解详情
                </div>
            </aside>

            <section class="content-area">
                <div id="library-view" class="view active">
                    <div class="view-header">
                        <h2>我的书架</h2>
                        <div class="view-controls">
                            <select id="sort-select">
                                <option value="recent">最近添加</option>
                                <option value="name">书名</option>
                                <option value="author">作者</option>
                            </select>
                            <div class="view-mode-toggle">
                                <button id="grid-view" class="view-btn active">网格</button>
                                <button id="list-view" class="view-btn">列表</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="books-container" id="books-container">
                        <!-- 书籍内容将在这里动态加载 -->
                    </div>
                </div>

                <div id="recent-view" class="view">
                    <h2>最近阅读</h2>
                    <p>这里显示最近阅读的书籍</p>
                </div>

                <div id="favorites-view" class="view">
                    <h2>我的收藏</h2>
                    <p>这里显示收藏的书籍</p>
                </div>

                <div id="notes-view" class="view">
                    <h2>我的笔记</h2>
                    <p>这里显示所有笔记</p>
                </div>

                <div id="reader-view" class="view">
                    <div class="reader-container">
                        <div class="reader-toolbar">
                            <button id="back-to-library">← 返回书架</button>
                            <div class="reader-progress">
                                <span id="current-page">1</span> / <span id="total-pages">100</span>
                            </div>
                            <div class="reader-controls">
                                <button id="bookmark-btn">🔖</button>
                                <button id="note-btn">📝</button>
                                <button id="tts-btn">🔊</button>
                            </div>
                        </div>
                        
                        <div class="reader-content" id="reader-content">
                            <p>阅读内容将在这里显示</p>
                        </div>
                        
                        <div class="reader-navigation">
                            <button id="prev-page" class="nav-btn">上一页</button>
                            <button id="next-page" class="nav-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 添加一些测试按钮 -->
    <div style="position: fixed; bottom: 20px; right: 20px; background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h4>测试功能</h4>
        <button onclick="addTestBook()" class="btn btn-primary" style="margin: 2px;">添加测试书籍</button><br>
        <button onclick="testNavigation()" class="btn btn-secondary" style="margin: 2px;">测试导航</button><br>
        <button onclick="testViewToggle()" class="btn btn-secondary" style="margin: 2px;">测试视图切换</button><br>
        <button onclick="showDebugInfo()" class="btn btn-secondary" style="margin: 2px;">显示调试信息</button>
    </div>

    <script src="src/main.js"></script>
    <script>
        // 测试函数
        function addTestBook() {
            console.log('添加测试书籍')
            app.addBook({
                id: Date.now().toString(),
                title: '测试书籍 ' + Math.floor(Math.random() * 100),
                author: '测试作者',
                cover: null,
                filePath: '/test/path.txt',
                progress: Math.floor(Math.random() * 100),
                addedAt: new Date().toISOString()
            })
        }

        function testNavigation() {
            console.log('测试导航功能')
            const views = ['library', 'recent', 'favorites', 'notes']
            const randomView = views[Math.floor(Math.random() * views.length)]
            app.setView(randomView)
            
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active')
                if (link.getAttribute('href') === '#' + randomView) {
                    link.classList.add('active')
                }
            })
        }

        function testViewToggle() {
            console.log('测试视图切换')
            const container = document.getElementById('books-container')
            if (container) {
                container.classList.toggle('list-view')
                console.log('当前视图模式:', container.classList.contains('list-view') ? '列表' : '网格')
            }
        }

        function showDebugInfo() {
            console.log('=== 调试信息 ===')
            console.log('当前视图:', app.currentView)
            console.log('书籍数量:', app.books.length)
            console.log('导航链接数量:', document.querySelectorAll('.nav-link').length)
            console.log('视图切换按钮:', {
                gridBtn: !!document.getElementById('grid-view'),
                listBtn: !!document.getElementById('list-view')
            })
            console.log('Tauri环境:', !!window.__TAURI__)
        }

        // 页面加载完成后的额外初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('测试页面加载完成')
            showDebugInfo()
        })
    </script>
</body>
</html>
