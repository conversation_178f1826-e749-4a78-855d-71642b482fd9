# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-append"
description = "Enables the append command without any pre-configured scope."
commands.allow = ["append"]

[[permission]]
identifier = "deny-append"
description = "Denies the append command without any pre-configured scope."
commands.deny = ["append"]
