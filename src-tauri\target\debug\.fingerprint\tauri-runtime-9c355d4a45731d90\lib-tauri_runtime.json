{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 451249050788002760, "deps": [[654232091421095663, "tauri_utils", false, 11379566753883280293], [3150220818285335163, "url", false, 16277201914255492281], [4143744114649553716, "raw_window_handle", false, 3687742720338333907], [7606335748176206944, "dpi", false, 23882016242874549], [8569119365930580996, "serde_json", false, 11475744921138336352], [9010263965687315507, "http", false, 4349961928876268954], [9689903380558560274, "serde", false, 6764526651753107293], [10806645703491011684, "thiserror", false, 2658131691907457673], [12943761728066819757, "build_script_build", false, 14763020576908811479], [14585479307175734061, "windows", false, 8237828940172028574], [16727543399706004146, "cookie", false, 6633006685850233324]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-9c355d4a45731d90\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}