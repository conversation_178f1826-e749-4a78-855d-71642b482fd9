use crate::core::models::*;
use anyhow::Result;
use std::path::Path;

/// 统一的文档接口
pub trait Document: Send + Sync {
    /// 获取文档元数据
    fn get_metadata(&self) -> Result<BookMetadata>;
    
    /// 获取总页数
    fn get_total_pages(&self) -> Result<u32>;
    
    /// 获取指定页面的内容
    fn get_page_content(&self, page: u32) -> Result<PageContent>;
    
    /// 获取目录/章节信息
    fn get_table_of_contents(&self) -> Result<Vec<ChapterInfo>>;
    
    /// 搜索文本
    fn search_text(&self, query: &str) -> Result<Vec<SearchResult>>;
    
    /// 提取封面图片
    fn extract_cover(&self) -> Result<Option<Vec<u8>>>;
    
    /// 获取文档的纯文本内容（用于TTS等功能）
    fn get_text_content(&self, start_page: u32, end_page: u32) -> Result<String>;
}

/// 章节信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ChapterInfo {
    pub title: String,
    pub start_page: u32,
    pub end_page: Option<u32>,
    pub level: u8, // 章节层级，1为一级标题，2为二级标题等
}

/// 搜索结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SearchResult {
    pub page_number: u32,
    pub context: String,
    pub match_start: usize,
    pub match_end: usize,
}

/// 文档工厂，根据文件类型创建相应的文档解析器
pub struct DocumentFactory;

impl DocumentFactory {
    pub fn create_document(file_path: &Path) -> Result<Box<dyn Document>> {
        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| anyhow::anyhow!("无法确定文件类型"))?;

        let format = FileFormat::from_extension(extension)
            .ok_or_else(|| anyhow::anyhow!("不支持的文件格式: {}", extension))?;

        match format {
            FileFormat::Epub => {
                let parser = crate::core::parser::EpubParser::new(file_path)?;
                Ok(Box::new(parser))
            }
            FileFormat::Pdf => {
                let parser = crate::core::parser::PdfParser::new(file_path)?;
                Ok(Box::new(parser))
            }
            FileFormat::Txt => {
                let parser = crate::core::parser::TxtParser::new(file_path)?;
                Ok(Box::new(parser))
            }
            _ => Err(anyhow::anyhow!("暂不支持的文件格式: {:?}", format)),
        }
    }
}

/// 文档管理器，负责文档的缓存和生命周期管理
pub struct DocumentManager {
    cache: std::collections::HashMap<String, Box<dyn Document>>,
    max_cache_size: usize,
}

impl DocumentManager {
    pub fn new(max_cache_size: usize) -> Self {
        Self {
            cache: std::collections::HashMap::new(),
            max_cache_size,
        }
    }

    pub fn get_document(&mut self, file_path: &Path) -> Result<&dyn Document> {
        let path_str = file_path.to_string_lossy().to_string();
        
        if !self.cache.contains_key(&path_str) {
            // 如果缓存已满，移除最旧的文档
            if self.cache.len() >= self.max_cache_size {
                if let Some(key) = self.cache.keys().next().cloned() {
                    self.cache.remove(&key);
                }
            }
            
            let document = DocumentFactory::create_document(file_path)?;
            self.cache.insert(path_str.clone(), document);
        }
        
        Ok(self.cache.get(&path_str).unwrap().as_ref())
    }

    pub fn clear_cache(&mut self) {
        self.cache.clear();
    }

    pub fn remove_document(&mut self, file_path: &Path) {
        let path_str = file_path.to_string_lossy().to_string();
        self.cache.remove(&path_str);
    }
}
