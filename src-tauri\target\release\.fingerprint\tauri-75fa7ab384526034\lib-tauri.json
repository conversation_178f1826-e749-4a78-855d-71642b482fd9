{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 17160455492222233904, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [654232091421095663, "tauri_utils", false, 15308955495522971671], [1200537532907108615, "url<PERSON><PERSON>n", false, 14708531954418702323], [2013030631243296465, "webview2_com", false, 8039702730747510435], [3150220818285335163, "url", false, 12663329439391652794], [3331586631144870129, "getrandom", false, 17283948573081985853], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [4494683389616423722, "muda", false, 14704211772130557989], [4919829919303820331, "serialize_to_javascript", false, 2814038814007313312], [5986029879202738730, "log", false, 15905267756897252431], [8569119365930580996, "serde_json", false, 13571012663895458450], [9010263965687315507, "http", false, 1862789691165185595], [9689903380558560274, "serde", false, 9841288889961963921], [10229185211513642314, "mime", false, 9651018063964816897], [10806645703491011684, "thiserror", false, 13847597657789702417], [11989259058781683633, "dunce", false, 486707178400923159], [12092653563678505622, "build_script_build", false, 5543743481887360298], [12304025191202589669, "tauri_runtime_wry", false, 12619710499971099843], [12393800526703971956, "tokio", false, 12330004968885259251], [12565293087094287914, "window_vibrancy", false, 18234502769180980590], [12943761728066819757, "tauri_runtime", false, 643590536472946368], [12986574360607194341, "serde_repr", false, 15487894934108931834], [13077543566650298139, "heck", false, 11675331362666342109], [13405681745520956630, "tauri_macros", false, 4902734456608512258], [13625485746686963219, "anyhow", false, 2431689092402825966], [14585479307175734061, "windows", false, 3114519431610511065], [16928111194414003569, "dirs", false, 10435552808141219587], [17155886227862585100, "glob", false, 4801171762843958688]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-75fa7ab384526034\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}