// 检查是否在Tauri环境中
let invoke;
if (window.__TAURI__) {
    invoke = window.__TAURI__.core.invoke;
} else {
    // 模拟invoke函数用于开发环境
    invoke = async (command, args) => {
        console.log(`模拟调用: ${command}`, args);

        // 模拟不同命令的返回值
        switch (command) {
            case 'open_file_dialog':
                return new Promise(resolve => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.txt,.epub,.pdf,.mobi,.azw3,.cbr,.cbz,.docx';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            resolve(file.name); // 返回文件名作为模拟路径
                        } else {
                            resolve(null);
                        }
                    };
                    input.click();
                });

            case 'parse_book_file':
                return {
                    id: Date.now().toString(),
                    title: args.filePath.replace(/\.[^/.]+$/, ""), // 移除扩展名
                    author: '示例作者',
                    cover_path: null,
                    file_path: args.filePath,
                    created_at: new Date().toISOString()
                };

            case 'get_all_books':
                return [];

            case 'get_book_content':
                return {
                    page_number: args.page,
                    elements: [
                        {
                            Paragraph: {
                                content: `这是第${args.page}页的示例内容。这是一个测试文档，展示墨读应用的基本功能。`,
                                style: null
                            }
                        }
                    ],
                    chapter_title: `第${Math.ceil(args.page / 10)}章`
                };

            default:
                throw new Error(`未知命令: ${command}`);
        }
    };
}

// 应用状态管理
class AppState {
    constructor() {
        this.currentView = 'library'
        this.books = []
        this.currentBook = null
        this.settings = {
            theme: 'light',
            fontSize: 16,
            lineHeight: 1.6,
            fontFamily: 'serif'
        }
    }

    setView(viewName) {
        // 隐藏所有视图
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active')
        })
        
        // 显示目标视图
        const targetView = document.getElementById(`${viewName}-view`)
        if (targetView) {
            targetView.classList.add('active')
            this.currentView = viewName
        }
    }

    addBook(bookData) {
        this.books.push(bookData)
        this.renderBooks()
    }

    renderBooks() {
        const container = document.getElementById('books-container')
        if (!container) return;

        if (this.books.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📚</div>
                    <h3>书架空空如也</h3>
                    <p>点击"导入书籍"开始您的阅读之旅</p>
                    <button class="btn btn-primary" id="empty-import-btn">导入第一本书</button>
                </div>
            `
            // 重新绑定空状态按钮事件
            const emptyImportBtn = document.getElementById('empty-import-btn')
            if (emptyImportBtn) {
                emptyImportBtn.addEventListener('click', importBooks)
            }
        } else {
            container.innerHTML = this.books.map(book => `
                <div class="book-card" data-book-id="${book.id}">
                    <div class="book-cover">
                        ${book.cover ?
                            `<img src="${book.cover}" alt="${book.title}">` :
                            `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">${book.title.charAt(0)}</div>`
                        }
                    </div>
                    <div class="book-info">
                        <h3 class="book-title">${book.title}</h3>
                        <p class="book-author">${book.author}</p>
                        <div class="book-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${book.progress || 0}%"></div>
                            </div>
                            <span class="progress-text">${book.progress || 0}%</span>
                        </div>
                    </div>
                </div>
            `).join('')

            // 重新绑定书籍卡片点击事件
            this.bindBookCardEvents()
        }
    }

    bindBookCardEvents() {
        const bookCards = document.querySelectorAll('.book-card')
        bookCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const bookId = card.getAttribute('data-book-id')
                if (bookId) {
                    openBook(bookId)
                }
            })
        })
    }

    async loadBooksFromDatabase() {
        try {
            const books = await invoke('get_all_books')
            this.books = books.map(book => ({
                id: book.id,
                title: book.title,
                author: book.author,
                cover: book.cover_path,
                filePath: book.file_path,
                progress: 0, // 需要从阅读进度中获取
                addedAt: book.created_at
            }))
            this.renderBooks()
        } catch (error) {
            console.error('加载书籍失败:', error)
        }
    }
}

// 全局应用实例
const app = new AppState()

// 导航功能
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link')
    console.log('设置导航，找到链接数量:', navLinks.length)

    navLinks.forEach((link, index) => {
        console.log(`绑定导航链接 ${index}:`, link.getAttribute('href'))
        link.addEventListener('click', (e) => {
            e.preventDefault()
            const href = link.getAttribute('href')
            const viewName = href ? href.substring(1) : 'library'

            console.log('点击导航:', viewName)

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'))
            link.classList.add('active')

            // 切换视图
            app.setView(viewName)
        })
    })
}

// 文件导入功能
async function importBooks() {
    try {
        // 调用Tauri后端API打开文件对话框
        const result = await invoke('open_file_dialog')
        if (result) {
            console.log('选择的文件:', result)
            // 这里将调用后端解析文件的功能
            await processImportedFile(result)
        }
    } catch (error) {
        console.error('导入文件失败:', error)
        alert('导入文件失败，请重试')
    }
}

async function processImportedFile(filePath) {
    try {
        // 显示加载状态
        showLoadingMessage('正在解析文件...')

        // 调用后端解析文件
        const bookData = await invoke('parse_book_file', { filePath })

        // 添加到本地状态
        app.addBook({
            id: bookData.id,
            title: bookData.title || '未知书名',
            author: bookData.author || '未知作者',
            cover: bookData.cover_path,
            filePath: filePath,
            progress: 0,
            addedAt: bookData.created_at
        })

        hideLoadingMessage()
        showSuccessMessage(`成功导入《${bookData.title}》`)
        console.log('成功导入书籍:', bookData)
    } catch (error) {
        hideLoadingMessage()
        console.error('解析文件失败:', error)
        showErrorMessage('不支持的文件格式或文件损坏')
    }
}

// 消息提示函数
function showLoadingMessage(message) {
    // 简单的加载提示，实际应用中可以使用更好的UI组件
    const loading = document.createElement('div')
    loading.id = 'loading-message'
    loading.style.cssText = `
        position: fixed; top: 20px; right: 20px;
        background: var(--primary-color); color: white;
        padding: 12px 20px; border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
    `
    loading.textContent = message
    document.body.appendChild(loading)
}

function hideLoadingMessage() {
    const loading = document.getElementById('loading-message')
    if (loading) {
        loading.remove()
    }
}

function showSuccessMessage(message) {
    showMessage(message, 'success')
}

function showErrorMessage(message) {
    showMessage(message, 'error')
}

function showMessage(message, type) {
    const msg = document.createElement('div')
    msg.style.cssText = `
        position: fixed; top: 20px; right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white; padding: 12px 20px; border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000; animation: slideIn 0.3s ease;
    `
    msg.textContent = message
    document.body.appendChild(msg)

    setTimeout(() => {
        msg.style.animation = 'slideOut 0.3s ease'
        setTimeout(() => msg.remove(), 300)
    }, 3000)
}

// 打开书籍阅读
function openBook(bookId) {
    const book = app.books.find(b => b.id === bookId)
    if (book) {
        app.currentBook = book
        app.setView('reader')
        loadBookContent(book)
    }
}

async function loadBookContent(book) {
    try {
        const content = await invoke('get_book_content', {
            filePath: book.filePath,
            page: 1
        })

        const readerContent = document.getElementById('reader-content')
        if (readerContent) {
            // 处理内容渲染
            let htmlContent = '';
            if (content.elements) {
                content.elements.forEach(element => {
                    if (element.Paragraph) {
                        htmlContent += `<p>${element.Paragraph.content}</p>`;
                    } else if (element.Text) {
                        htmlContent += `<span>${element.Text.content}</span>`;
                    } else if (element.Heading) {
                        htmlContent += `<h${element.Heading.level}>${element.Heading.content}</h${element.Heading.level}>`;
                    }
                });
            } else if (typeof content === 'string') {
                htmlContent = content;
            } else {
                htmlContent = '<p>无法显示内容</p>';
            }

            readerContent.innerHTML = htmlContent;
        }

        // 更新页面信息
        const currentPageEl = document.getElementById('current-page')
        if (currentPageEl) {
            currentPageEl.textContent = '1'
        }

        const totalPagesEl = document.getElementById('total-pages')
        if (totalPagesEl) {
            totalPagesEl.textContent = '100' // 暂时固定值
        }

    } catch (error) {
        console.error('加载书籍内容失败:', error)
        const readerContent = document.getElementById('reader-content')
        if (readerContent) {
            readerContent.innerHTML = '<p>加载失败，请重试</p>'
        }
    }
}

// 阅读器控制
function setupReaderControls() {
    // 返回书架
    document.getElementById('back-to-library')?.addEventListener('click', () => {
        app.setView('library')
    })
    
    // 翻页控制
    document.getElementById('prev-page')?.addEventListener('click', () => {
        // 实现上一页逻辑
        console.log('上一页')
    })
    
    document.getElementById('next-page')?.addEventListener('click', () => {
        // 实现下一页逻辑
        console.log('下一页')
    })
    
    // 书签功能
    document.getElementById('bookmark-btn')?.addEventListener('click', () => {
        console.log('添加书签')
    })
    
    // 笔记功能
    document.getElementById('note-btn')?.addEventListener('click', () => {
        console.log('添加笔记')
    })
    
    // TTS功能
    document.getElementById('tts-btn')?.addEventListener('click', () => {
        console.log('开始朗读')
    })
}

// 设置功能
function setupSettings() {
    document.getElementById('settings-btn')?.addEventListener('click', () => {
        console.log('打开设置')
        // 这里可以打开设置对话框
    })
}

// 视图模式切换
function setupViewModeToggle() {
    const gridViewBtn = document.getElementById('grid-view')
    const listViewBtn = document.getElementById('list-view')
    const booksContainer = document.getElementById('books-container')

    console.log('设置视图切换按钮:', { gridViewBtn, listViewBtn, booksContainer })

    if (gridViewBtn) {
        gridViewBtn.addEventListener('click', () => {
            console.log('切换到网格视图')
            if (booksContainer) {
                booksContainer.classList.remove('list-view')
            }
            gridViewBtn.classList.add('active')
            if (listViewBtn) {
                listViewBtn.classList.remove('active')
            }
        })
    }

    if (listViewBtn) {
        listViewBtn.addEventListener('click', () => {
            console.log('切换到列表视图')
            if (booksContainer) {
                booksContainer.classList.add('list-view')
            }
            listViewBtn.classList.add('active')
            if (gridViewBtn) {
                gridViewBtn.classList.remove('active')
            }
        })
    }
}

// 排序功能
function setupSorting() {
    const sortSelect = document.getElementById('sort-select')
    sortSelect?.addEventListener('change', (e) => {
        const sortBy = e.target.value
        sortBooks(sortBy)
    })
}

function sortBooks(sortBy) {
    switch (sortBy) {
        case 'name':
            app.books.sort((a, b) => a.title.localeCompare(b.title))
            break
        case 'author':
            app.books.sort((a, b) => a.author.localeCompare(b.author))
            break
        case 'recent':
        default:
            app.books.sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt))
            break
    }
    app.renderBooks()
}

// 应用初始化
function initApp() {
    console.log('墨读应用启动中...')

    // 等待DOM完全加载
    setTimeout(() => {
        // 设置导航
        setupNavigation()

        // 设置阅读器控制
        setupReaderControls()

        // 设置其他功能
        setupSettings()

        // 设置视图模式切换
        setupViewModeToggle()

        // 设置排序功能
        setupSorting()

        // 绑定导入按钮
        const importBtn = document.getElementById('import-btn')
        if (importBtn) {
            importBtn.addEventListener('click', importBooks)
        }

        // 从数据库加载书籍
        app.loadBooksFromDatabase()

        console.log('墨读应用启动完成')
    }, 100)
}

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp)

// 导出全局函数供HTML使用
window.importBooks = importBooks
window.openBook = openBook
