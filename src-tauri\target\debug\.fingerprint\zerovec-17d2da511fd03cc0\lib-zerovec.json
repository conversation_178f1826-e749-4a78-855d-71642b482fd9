{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2225463790103693989, "path": 7588903603151011990, "deps": [[9620753569207166497, "zerovec_derive", false, 16390555524858021372], [10706449961930108323, "yoke", false, 14851519874395111668], [17046516144589451410, "zerofrom", false, 14795419134903407573]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-17d2da511fd03cc0\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}